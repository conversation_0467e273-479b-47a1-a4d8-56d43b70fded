import { useState, useEffect, useCallback } from "react"

import "@/style.css"
import { VideoList, BottomToolbar } from "@/components"
import type { VideoData } from "@/types"
import {
  getFilteredRequests,
  getCurrentTabId,
  convertRequestsToVideoData,
  connectToBackground,
  listenToBackgroundUpdates,
  clearFilteredRequestsByTab
} from "@/utils"

function IndexPopup() {
  const [videoList, setVideoList] = useState<VideoData[]>([])
  const [currentTabId, setCurrentTabId] = useState<number | undefined>(undefined)

  // 获取视频数据
  const loadVideoData = useCallback(async () => {
    try {
      // 获取当前标签页ID
      const tabId = await getCurrentTabId()
      setCurrentTabId(tabId)

      // 获取过滤的请求列表
      const filteredRequests = await getFilteredRequests(tabId)

      // 转换为VideoData格式
      const videoData = convertRequestsToVideoData(filteredRequests)

      setVideoList(videoData)
      console.log("✅ Popup加载到视频数据:", videoData.length, "条")
    } catch (error) {
      console.error("❌ Popup加载视频数据失败:", error)
      setVideoList([])
    }
  }, [])

  // 组件挂载时建立连接和加载数据
  useEffect(() => {
    // 建立与background的连接
    const port = connectToBackground()

    // 初始加载数据
    loadVideoData()

    // 监听background更新
    const cleanup = listenToBackgroundUpdates(() => {
      console.log("🔄 收到background更新通知，重新加载数据")
      loadVideoData()
    })

    // 清理函数
    return () => {
      cleanup()
      port?.disconnect()
    }
  }, [loadVideoData])

  // 清空当前标签页的嗅探记录
  const handleClearCurrentTab = useCallback(async () => {
    try {
      const tabId = await getCurrentTabId()
      if (tabId) {
        const success = await clearFilteredRequestsByTab(tabId)
        if (success) {
          console.log("✅ 已清空当前标签页的嗅探记录")
          // 清空后重新加载数据
          setVideoList([])
        } else {
          console.error("❌ 清空当前标签页的嗅探记录失败")
        }
      }
    } catch (error) {
      console.error("❌ 清空当前标签页的嗅探记录异常:", error)
    }
  }, [])

  return (
    <div className="w-[500px] h-[500px] bg-gray-100 border border-gray-300 flex flex-col">
      {/* 主内容区域 - 为底部工具栏预留空间 */}
      <div className="flex-1 overflow-hidden" style={{ paddingBottom: '40px' }}>
        {videoList.length === 0 ? (
          // 空状态
          <div className="flex flex-col justify-center items-center p-2 gap-2.5 h-full rounded-t-lg">
            <div className="text-sm font-normal text-gray-500 text-center leading-relaxed">
              没有在当前标签页检测到任何视频<br />
              点击播放视频以帮助检测文件
            </div>
          </div>
        ) : (
          // 有视频时显示列表
          <div className="flex flex-col items-start p-2 gap-2.5 h-full overflow-y-auto rounded-t-lg">
            <VideoList videos={videoList} tabId={currentTabId} />
          </div>
        )}
      </div>

      {/* 底部工具栏 - 固定在底部 */}
      <div className="absolute bottom-0 left-0 right-0 z-10">
        <BottomToolbar variant="popup" onClearCurrentTab={handleClearCurrentTab} />
      </div>
    </div>
  )
}

export default IndexPopup
