// 网络请求过滤器
// 负责根据配置的规则过滤和筛选网络请求

import type { RequestInfo, FilterOptions, FilterRule } from "@/types/network"
import { extractExtensionFromUrl } from "@/utils/mediaUtils"

/**
 * 请求过滤器类
 * 根据扩展名、Content-Type和正则表达式规则过滤请求
 */
export class RequestFilter {
  // 默认过滤选项配置
  private filterOptions: FilterOptions = {
    Ext: [
      // 加一个是否启用属性
      { ext: "flv", enabled: true },
      { ext: "hlv", enabled: true },
      { ext: "f4v", enabled: true },
      { ext: "mp4", enabled: true },
      { ext: "mp3", enabled: true },
      { ext: "wma", enabled: true },
      { ext: "wav", enabled: true },
      { ext: "m4a", enabled: true },
      { ext: "webm", enabled: true },
      { ext: "ogg", enabled: true },
      { ext: "ogv", enabled: true },
      { ext: "aac", enabled: true },
      { ext: "mov", enabled: true },
      { ext: "mkv", enabled: true },
      { ext: "m4s", enabled: true },
      { ext: "m3u8", enabled: true },
      { ext: "m3u", enabled: true },
      { ext: "mpeg", enabled: true },
      { ext: "avi", enabled: true },
      { ext: "wmv", enabled: true },
      { ext: "asf", enabled: true },
      { ext: "movie", enabled: true },
      { ext: "divx", enabled: true },
      { ext: "mpeg4", enabled: true },
      { ext: "vid", enabled: true },
      { ext: "mpd", enabled: true },
      { ext: "weba", enabled: true },
      { ext: "opus", enabled: true },
      { ext: "ts", enabled: false },
    ],
    Type: [
      { type: "audio/*" },
      { type: "video/*" },
      { type: "application/ogg" },
      { type: "application/vnd.apple.mpegurl" },
      { type: "application/x-mpegurl" },
      { type: "application/mpegurl" },
      { type: "application/octet-stream-m3u8" },
      { type: "application/dash+xml" },
      { type: "application/m4s" }
    ],
    Regex: [
      {
        regex: "https://cache\\.video\\.[a-z]*\\.com/dash\\?tvid=.*",
        type: "ig",
        ext: "json"
      },
      {
        regex: ".*\\.bilivideo\\.(com|cn).*\\/live-bvc\\/.*m4s",
        type: "ig",
        blackList: true
      }
    ]
  }

  /**
   * 获取请求的Content-Type
   */
  private getContentType(requestInfo: RequestInfo): string {
    if (requestInfo.responseHeaders) {
      // 查找content-type头
      const contentTypeHeader = requestInfo.responseHeaders.find(
        header => header.name.toLowerCase() === 'content-type'
      )
      return contentTypeHeader?.value || ''
    }
    return ''
  }

  /**
   * 检查扩展名过滤规则
   */
  private checkExtensionFilter(requestInfo: RequestInfo): boolean {
    const urlExtension = extractExtensionFromUrl(requestInfo.url)
    if (!urlExtension) return false

    return this.filterOptions.Ext.some(rule =>
      rule.ext && rule.ext.toLowerCase() === urlExtension
    )
  }

  /**
   * 检查Content-Type过滤规则
   */
  private checkTypeFilter(requestInfo: RequestInfo): boolean {
    const contentType = this.getContentType(requestInfo)
    if (!contentType) return false

    return this.filterOptions.Type.some(rule => {
      if (!rule.type) return false
      const ruleType = rule.type.toLowerCase()

      // 处理通配符匹配（如 audio/*）
      if (ruleType.endsWith('/*')) {
        const prefix = ruleType.slice(0, -2)
        return contentType.startsWith(prefix)
      }

      // 精确匹配
      return contentType.includes(ruleType)
    })
  }

  /**
   * 检查正则表达式过滤规则
   */
  private checkRegexFilter(requestInfo: RequestInfo): boolean {
    for (const rule of this.filterOptions.Regex) {
      if (!rule.regex) continue
      try {
        const flags = rule.type === 'ig' ? 'gi' : 'g'
        const regex = new RegExp(rule.regex, flags)

        if (regex.test(requestInfo.url)) {
          // 如果是黑名单规则，匹配到则不应该显示
          if (rule.blackList) {
            return false
          }
          // 如果是白名单规则，匹配到则应该显示
          return true
        }
      } catch (error) {
        console.warn('正则表达式规则无效:', rule.regex, error)
      }
    }

    return false
  }

  /**
   * 检查请求是否应该被过滤显示
   * @param requestInfo 请求信息
   * @returns true表示应该显示，false表示应该过滤掉
   */
  shouldShowRequest(requestInfo: RequestInfo): boolean {
    // 首先检查黑名单正则规则
    for (const rule of this.filterOptions.Regex) {
      if (rule.blackList && rule.regex) {
        try {
          const flags = rule.type === 'ig' ? 'gi' : 'g'
          const regex = new RegExp(rule.regex, flags)

          if (regex.test(requestInfo.url)) {
            return false // 黑名单匹配，不显示
          }
        } catch (error) {
          console.warn('黑名单正则表达式规则无效:', rule.regex, error)
        }
      }
    }

    // 检查是否匹配任一白名单规则
    const matchesExtension = this.checkExtensionFilter(requestInfo)
    const matchesType = this.checkTypeFilter(requestInfo)
    const matchesRegex = this.checkRegexFilter(requestInfo)
    if (matchesExtension) {
      // 检查对应扩展名的扩展名过滤规则是否启用,若未启用则强制不显示
      const urlExtension = extractExtensionFromUrl(requestInfo.url)
      const extensionRule = this.filterOptions.Ext.find(rule => rule.ext === urlExtension)
      if (extensionRule && !extensionRule.enabled) {
        return false
      }
    }

    // 只要匹配任意一个白名单规则就显示
    return matchesExtension || matchesType || matchesRegex
  }
}

// 导出单例实例
export const requestFilter = new RequestFilter()
