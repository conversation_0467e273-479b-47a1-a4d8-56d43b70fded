# SnapAny Extension

基于 Plasmo 框架的浏览器扩展开发项目。

## 技术栈

- **Plasmo**: 现代浏览器插件开发框架
- **TypeScript**: 类型安全的 JavaScript
- **React**: 用户界面库
- **TailwindCSS**: 实用优先的 CSS 框架
- **HLS.js**: M3U8 流媒体处理库

## 开发环境设置

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

### 加载扩展到浏览器

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `build/chrome-mv3-dev` 目录

### 启动外部下载服务

项目使用外部下载页面处理文件下载，需要启动本地HTTP服务器：

```bash
# 使用 Python
python -m http.server 8081

# 或使用 Node.js
npx serve -p 8081
```

下载器页面访问地址：`http://localhost:3456/`（独立的 React 项目：snapany-downloader-web）

## 项目结构

```text
src/
├── background/           # 后台脚本
│   ├── handlers/        # 消息和连接处理器
│   ├── services/        # 核心服务模块
│   └── index.ts         # 后台脚本入口
├── components/          # React 组件
├── contents/            # 内容脚本
├── types/              # TypeScript 类型定义
├── utils/              # 工具函数
├── popup.tsx           # 弹窗界面
├── sidepanel.tsx       # 侧边栏界面
└── style.css           # 全局样式

public/
└── (下载器页面已迁移到独立项目 snapany-downloader-web)
```

## 开发指南

### 核心模块说明

- **RequestMonitor**: 网络请求监控和过滤
- **DownloadManager**: 文件下载管理
- **HeaderManager**: 请求头处理
- **MessageHandler**: 消息路由和处理
- **SidepanelManager**: 侧边栏状态管理

### 通信机制

项目使用多种通信方式实现组件间的数据交换：

#### 1. Background ↔ Popup/Sidepanel 通信

**消息传递方式**：

```typescript
// 发送消息到 background
import { sendMessageToBackground } from '@/utils/chromeMessages'

const response = await sendMessageToBackground({
  type: MessageType.GET_FILTERED_REQUESTS,
  tabId: currentTabId
})
```

**持久连接方式**：

```typescript
// Popup 建立持久连接
const port = chrome.runtime.connect({ name: "popup" })
port.onMessage.addListener((message) => {
  // 处理来自 background 的消息
})

// Sidepanel 建立持久连接
const port = chrome.runtime.connect({ name: "sidepanel" })
```

#### 2. Background ↔ Content Script 通信

**Background 监听消息**：

```typescript
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  messageHandler.handleMessage(message, sender, sendResponse)
  return true // 保持异步响应通道
})
```

**Content Script 发送消息**：

```typescript
const response = await chrome.runtime.sendMessage({
  type: "DOWNLOAD_FILE_WITH_HEADERS",
  payload: downloadData
})
```

#### 3. 外部页面 ↔ Content Script 通信

**页面向 Content Script 发送消息**：

```javascript
window.postMessage({
  type: 'GET_DOWNLOAD_DATA_BY_ID',
  data: { requestId }
}, '*')
```

**Content Script 响应页面消息**：

```typescript
window.addEventListener('message', async (event) => {
  if (event.source !== window) return

  const { type, data } = event.data
  // 处理消息并响应
  window.postMessage({
    type: 'DOWNLOAD_DATA_RESPONSE',
    data: response
  }, '*')
})
```

#### 4. 消息类型定义

项目定义了完整的消息类型枚举：

```typescript
export enum MessageType {
  // 基础功能
  CONTENT_SCRIPT_READY = "CONTENT_SCRIPT_READY",

  // 过滤请求
  GET_FILTERED_REQUESTS = "GET_FILTERED_REQUESTS",
  CLEAR_FILTERED_REQUESTS = "CLEAR_FILTERED_REQUESTS",

  // 下载管理
  CREATE_DOWNLOADER = "CREATE_DOWNLOADER",
  DOWNLOAD_FILE_WITH_HEADERS = "DOWNLOAD_FILE_WITH_HEADERS",

  // 请求头管理
  SET_REQUEST_HEADERS = "SET_REQUEST_HEADERS",

  // 其他功能
  TOGGLE_SIDEPANEL = "TOGGLE_SIDEPANEL"
}
```

### 开发注意事项

1. **消息传递**: 所有异步消息都需要返回 `true` 保持响应通道开放
2. **权限管理**: 项目需要 `webRequest`、`downloads`、`storage`、`declarativeNetRequest`、`sidePanel` 等权限
3. **跨域处理**: 通过 `declarativeNetRequest` API 动态注入请求头解决跨域问题
4. **内存管理**: 大文件下载使用流式处理和 ArrayBuffer 转换避免内存溢出(此处待修复，需要实行边下边存，实现可参考猫抓)
5. **错误处理**: 所有异步操作都包含完整的错误处理和超时机制
6. **存储管理**: 使用 `chrome.storage.local` 进行数据持久化，注意清理过期数据
7. **外部服务依赖**: 下载功能依赖独立的下载器项目 (localhost:3456)

### 重要配置文件

#### 1. 权限配置 (package.json)

```json
{
  "manifest": {
    "host_permissions": [
      "https://*/*",
      "http://*/*"
    ],
    "permissions": [
      "webRequest",        // 网络请求监控
      "tabs",             // 标签页操作
      "activeTab",        // 当前标签页访问
      "storage",          // 本地存储
      "declarativeNetRequest", // 动态请求头注入
      "sidePanel",        // 侧边栏功能
      "downloads",        // 文件下载
      "scripting"         // 脚本注入
    ],
    "side_panel": {
      "default_path": "sidepanel.html"
    }
  }
}
```

#### 2. Content Script 配置

外部下载器页面的内容脚本配置：

```typescript
// src/contents/downloader-bridge.ts
export const config = {
  matches: ["http://localhost:3456/*"]  // 匹配独立下载器项目
}
```

#### 3. 存储机制

项目使用分层存储策略：

**Chrome Storage (持久化存储)：**
```typescript
// 视图模式存储
await chrome.storage.local.set({ snapany_view_mode: 'sidepanel' })

// 下载数据存储（供外部下载器访问）
await chrome.storage.local.set({
  [`downloadData_${requestId}`]: downloadData
})
```

**内存存储 (运行时状态)：**
```typescript
// 网络请求监控数据 (按标签页隔离)
private filteredRequestsByTab = new Map<number, FilteredRequestInfo[]>()

// 下载任务状态跟踪
private activeDownloads = new Set<string>()
private downloadTasksByTab = new Map<number, Set<string>>()

// 请求头规则映射
private requestHeaderRules = new Map<string, number>()
```
```

### 调试技巧

#### 1. 扩展调试

- 使用 `chrome://extensions/` 查看扩展详情和错误
- 后台脚本调试：点击"检查视图 service worker"
- 内容脚本调试：在页面中按 F12，查看 Console
- 网络请求监控：在 DevTools Network 面板查看拦截的请求

#### 2. 消息通信调试

```typescript
// 在消息发送前添加日志
console.log("发送消息:", message)
const response = await sendMessageToBackground(message)
console.log("收到响应:", response)

// 在 background 中监控所有消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("Background 收到消息:", message, "来自:", sender)
  // ... 处理逻辑
})
```

#### 3. 外部下载器调试

- 检查本地服务器是否在 8081 端口运行
- 在下载器页面按 F12 查看控制台日志
- 检查 `chrome.storage.local` 中的下载数据
- 监控 `window.postMessage` 通信

#### 4. 网络请求调试

```typescript
// 在 RequestMonitor 中添加详细日志
console.log("拦截到请求:", {
  url: details.url,
  method: details.method,
  type: details.type,
  tabId: details.tabId
})
```

### 常见问题和解决方案

#### 1. 外部下载器无法访问

**问题**：下载器页面显示无法连接或内容脚本未加载

**解决方案**：

- 确保本地 HTTP 服务器在 8081 端口运行
- 检查 `src/contents/downloader-bridge.ts` 的 matches 配置
- 验证扩展是否正确加载了内容脚本

#### 2. 消息通信失败

**问题**：Background 和 Popup/Sidepanel 之间消息传递失败

**解决方案**：

- 检查消息类型是否在 `MessageType` 枚举中定义
- 确保异步消息处理函数返回 `true`
- 验证消息格式是否符合 `Message` 接口定义

#### 3. 网络请求拦截不生效

**问题**：无法检测到媒体文件请求

**解决方案**：

- 检查 `webRequest` 权限是否正确配置
- 验证过滤规则是否包含目标文件类型
- 确认 `host_permissions` 包含目标网站域名

#### 4. 下载功能异常

**问题**：文件下载失败或浏览器冻结

**解决方案**：

- 检查 `downloads` 权限是否已授予
- 验证 `declarativeNetRequest` 规则是否正确设置
- 对于大文件，确保使用流式处理避免内存溢出

### 开发最佳实践

#### 1. 错误处理

```typescript
// 统一的错误处理模式
try {
  const response = await sendMessageToBackground(message)
  if (!response.success) {
    console.error("操作失败:", response.error)
    // 用户友好的错误提示
  }
} catch (error) {
  console.error("异常:", error)
  // 降级处理
}
```

#### 2. 内存管理

```typescript
// 及时清理大对象
const cleanup = () => {
  if (hls) {
    hls.destroy()
    hls = null
  }
}

// 组件卸载时清理
useEffect(() => {
  return cleanup
}, [])
```

#### 3. 性能优化

- 使用 `chrome.storage.local` 而非 `localStorage`
- 避免频繁的消息传递，使用批量操作
- 对大文件使用流式处理
- 及时清理过期的存储数据

#### 4. 代码组织

- 按功能模块组织代码 (`services/`, `handlers/`, `utils/`)
- 使用 TypeScript 类型定义确保类型安全
- 统一的消息类型和接口定义
- 模块化的依赖注入设计

## 构建和打包

```bash
# 开发构建
pnpm dev

# 生产构建
pnpm build

# 打包为 zip 文件，注意需先执行生产构建
pnpm package
```

构建产物：

- `build/chrome-mv3-dev/` - 开发版本
- `build/chrome-mv3-prod/` - 生产版本
- `build/chrome-mv3-prod.zip` - 打包文件

## 更多资源

- [Plasmo 官方文档](https://docs.plasmo.com/)
- [Chrome Extension API 文档](https://developer.chrome.com/docs/extensions/)
