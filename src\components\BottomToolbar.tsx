/**
 * 底部工具栏组件
 * 提供视图模式切换和清空功能
 */

import type { BottomToolbarProps } from "@/types"
import { toggleSidepanel, setCurrentViewMode } from "@/utils"

export default function BottomToolbar({ variant = "popup", onClearCurrentTab }: BottomToolbarProps) {

  /**
   * 处理视图模式切换
   * 支持在popup和sidepanel之间切换，并自动管理窗口状态
   */
  const handleViewModeToggle = async () => {
    try {
      if (variant === "popup") {
        // === 从popup切换到sidepanel ===
        console.log("切换到侧边栏模式")

        // 保存新的视图模式到本地存储
        await setCurrentViewMode("sidepanel")

        // 配置侧边栏选项和行为
        const success = await toggleSidepanel()

        if (success) {
          // 在用户手势响应中直接打开侧边栏
          try {
            const currentWindow = await chrome.windows.getCurrent()
            await chrome.sidePanel.open({ windowId: currentWindow.id })
            console.log("侧边栏已自动打开")
          } catch (openError) {
            console.warn("自动打开侧边栏失败:", openError)
          }

          // 关闭当前popup窗口
          window.close()
        } else {
          console.error("准备侧边栏失败")
          // 失败时回滚到原来的模式
          await setCurrentViewMode("popup")
        }
      } else {
        // === 从sidepanel切换到popup ===
        console.log("切换到弹窗模式")

        // 保存新的视图模式到本地存储
        await setCurrentViewMode("popup")

        // 禁用侧边栏的自动打开行为
        try {
          await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: false })
        } catch (error) {
          console.warn("无法设置侧边栏行为:", error)
        }

        // 关闭当前侧边栏窗口
        window.close()

        // 注意: popup不能自动弹出，用户需要手动点击扩展图标
        console.log("已切换到弹窗模式，请点击扩展图标打开弹窗")
      }
    } catch (error) {
      console.error("切换视图模式失败:", error)
    }
  }

  return (
    <div className={`flex justify-between items-center w-full h-10 bg-white`}>
      {/* 视图模式切换按钮 - 根据当前模式显示不同图标 */}
      <button
        type="button"
        className="flex justify-center items-center w-10 h-10 rounded-full hover:bg-gray-100"
        onClick={handleViewModeToggle}
        title={variant === "popup" ? "切换到侧边栏模式" : "切换到弹窗模式"}
      >
        {variant === "popup" ? (
          // Popup模式图标 - 表示切换到侧边栏
          <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.00002 1.27454V11.9412M4.00002 5.27454L5.33335 6.60787L4.00002 7.9412M0.666687 2.60787C0.666687 2.25425 0.807163 1.91511 1.05721 1.66506C1.30726 1.41501 1.6464 1.27454 2.00002 1.27454H10C10.3536 1.27454 10.6928 1.41501 10.9428 1.66506C11.1929 1.91511 11.3334 2.25425 11.3334 2.60787V10.6079C11.3334 10.9615 11.1929 11.3006 10.9428 11.5507C10.6928 11.8007 10.3536 11.9412 10 11.9412H2.00002C1.6464 11.9412 1.30726 11.8007 1.05721 11.5507C0.807163 11.3006 0.666687 10.9615 0.666687 10.6079V2.60787Z"
              stroke="#6B7280"
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ) : (
          // Sidepanel模式图标 - 表示切换到弹窗
          <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0.666656 4.84755H11.3333M4.66666 9.51422L5.99999 8.18089L7.33332 9.51422M0.666656 10.8476V2.84755C0.666656 2.49393 0.807132 2.15479 1.05718 1.90475C1.30723 1.6547 1.64637 1.51422 1.99999 1.51422H9.99999C10.3536 1.51422 10.6928 1.6547 10.9428 1.90475C11.1928 2.15479 11.3333 2.49393 11.3333 2.84755V10.8476C11.3333 11.2012 11.1928 11.5403 10.9428 11.7904C10.6928 12.0404 10.3536 12.1809 9.99999 12.1809H1.99999C1.64637 12.1809 1.30723 12.0404 1.05718 11.7904C0.807132 11.5403 0.666656 11.2012 0.666656 10.8476Z"
              stroke="#6B7280"
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </button>

      {/* 清空按钮 - 清空当前标签页的嗅探记录 */}
      <button
        type="button"
        className="flex justify-center items-center w-10 h-10 rounded-full hover:bg-gray-100"
        onClick={onClearCurrentTab}
        title="清空当前标签页的嗅探记录"
      >
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
          {/* 垃圾桶主体 */}
          <path
            d="M12.7037 3.55522H9.85189V2.08153C9.85189 1.69069 9.70166 1.31585 9.43425 1.03948C9.16684 0.763112 8.80415 0.607849 8.42597 0.607849H5.57411C5.19594 0.607849 4.83325 0.763112 4.56583 1.03948C4.29842 1.31585 4.14819 1.69069 4.14819 2.08153V3.55522H1.29634C1.10725 3.55522 0.925903 3.63285 0.792196 3.77103C0.65849 3.90922 0.583374 4.09664 0.583374 4.29206C0.583374 4.48748 0.65849 4.6749 0.792196 4.81309C0.925903 4.95127 1.10725 5.0289 1.29634 5.0289H2.0093V13.1342C2.0093 13.525 2.15953 13.8998 2.42694 14.1762C2.69436 14.4526 3.05705 14.6078 3.43523 14.6078H10.5649C10.943 14.6078 11.3057 14.4526 11.5731 14.1762C11.8406 13.8998 11.9908 13.525 11.9908 13.1342V5.0289H12.7037C12.8928 5.0289 13.0742 4.95127 13.2079 4.81309C13.3416 4.6749 13.4167 4.48748 13.4167 4.29206C13.4167 4.09664 13.3416 3.90922 13.2079 3.77103C13.0742 3.63285 12.8928 3.55522 12.7037 3.55522ZM5.57411 2.08153H8.42597V3.55522H5.57411V2.08153ZM10.5649 13.1342H3.43523V5.0289H10.5649V13.1342Z"
            fill="#6B7280"
          />
          {/* 垃圾桶内部分隔线 - 左侧 */}
          <path
            d="M5.57411 5.76574C5.38503 5.76574 5.20368 5.84338 5.06997 5.98156C4.93627 6.11974 4.86115 6.30716 4.86115 6.50259V11.6605C4.86115 11.8559 4.93627 12.0433 5.06997 12.1815C5.20368 12.3197 5.38503 12.3973 5.57411 12.3973C5.7632 12.3973 5.94455 12.3197 6.07826 12.1815C6.21196 12.0433 6.28708 11.8559 6.28708 11.6605V6.50259C6.28708 6.30716 6.21196 6.11974 6.07826 5.98156C5.94455 5.84338 5.7632 5.76574 5.57411 5.76574Z"
            fill="#6B7280"
          />
          {/* 垃圾桶内部分隔线 - 右侧 */}
          <path
            d="M8.42597 5.76574C8.23688 5.76574 8.05553 5.84338 7.92183 5.98156C7.78812 6.11974 7.713 6.30716 7.713 6.50259V11.6605C7.713 11.8559 7.78812 12.0433 7.92183 12.1815C8.05553 12.3197 8.23688 12.3973 8.42597 12.3973C8.61506 12.3973 8.7964 12.3197 8.93011 12.1815C9.06381 12.0433 9.13893 11.8559 9.13893 11.6605V6.50259C9.13893 6.30716 9.06381 6.11974 8.93011 5.98156C8.7964 5.84338 8.61506 5.76574 8.42597 5.76574Z"
            fill="#6B7280"
          />
        </svg>
      </button>
    </div>
  )
}