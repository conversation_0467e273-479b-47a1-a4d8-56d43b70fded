// 下载管理模块
// 处理文件下载、保存和下载数据管理

import type { DownloadData, Response } from "../../types/network"
import type { HeaderManager } from "./header"

export class DownloadManager {
  private headerManager?: HeaderManager
  private activeDownloads = new Set<string>() // 跟踪正在进行的下载
  private downloadTasksByTab = new Map<number, Set<string>>() // 按标签页跟踪下载任务

  constructor(headerManager?: HeaderManager) {
    this.headerManager = headerManager
  }



  // 处理通过单个ID获取下载数据请求（用于外部页面）
  async handleGetDownloadDataById(
    requestId: string | undefined,
    sendResponse: (response: Response) => void
  ) {
    try {
      if (!requestId) {
        sendResponse({
          success: false,
          error: "缺少请求ID参数"
        })
        return
      }

      // 从Chrome storage中获取下载数据
      const result = await chrome.storage.local.get(`downloadData_${requestId}`)
      const downloadData: DownloadData | undefined = result[`downloadData_${requestId}`]

      if (!downloadData) {
        sendResponse({
          success: false,
          error: "找不到指定的下载数据"
        })
        return
      }

      sendResponse({
        success: true,
        data: downloadData
      })
    } catch (error) {
      console.error("获取下载数据失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "获取下载数据失败"
      })
    }
  }

  // 处理设置请求头
  async handleDownloadFileWithHeaders(
    payload: { url: string, filename: string, requestHeaders: chrome.webRequest.HttpHeader[], pageUrl: string, pageTaskId?: string },
    sendResponse: (response: Response) => void,
    tabId: number
  ) {
    console.log("handleDownloadFileWithHeaders-------", payload)
    console.log("sendResponse", sendResponse)
    console.log("tabId", tabId)
    try {
      const { url, filename, requestHeaders, pageUrl } = payload

      if (!this.headerManager) {
        throw new Error('请求头管理器未初始化')
      }

      // 设置请求头，传递pageTaskId
      await this.headerManager.handleSetRequestHeaders({
        url,
        requestHeaders,
        pageUrl,
        tabId,
        pageTaskId: payload.pageTaskId
      }, (response: Response) => {
        if (!response.success) {
          throw new Error(response.error || '设置请求头失败')
        }
      })

      console.log(`设置请求头完成: ${filename}`)

      // 先回复给调用者
      sendResponse({
        success: true,
        data: {
          filename,
          tabId,
          message: '请求头设置成功'
        }
      })

    } catch (error) {
      console.error("设置请求头失败:", error)

      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "设置请求头失败"
      })
    }
  }

  // 清理指定标签页的所有下载任务
  cleanupTabTasks(tabId: number): void {
    console.log(`清理标签页 ${tabId} 的下载任务`)

    if (this.downloadTasksByTab.has(tabId)) {
      const tasks = this.downloadTasksByTab.get(tabId)!

      // 清理所有相关的活跃下载
      for (const requestId of tasks) {
        const taskKey = `${tabId}-${requestId}`
        this.activeDownloads.delete(taskKey)
      }

      // 删除标签页记录
      this.downloadTasksByTab.delete(tabId)
      console.log(`已清理标签页 ${tabId} 的 ${tasks.size} 个下载任务`)
    }
  }


}
